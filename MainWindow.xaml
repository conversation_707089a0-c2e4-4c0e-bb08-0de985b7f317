﻿<Window x:Class="TeklaTool.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:TeklaTool"
        xmlns:views="clr-namespace:TeklaTool.Views"
        xmlns:viewModels="clr-namespace:TeklaTool.ViewModels"
        mc:Ignorable="d"
        Title="Tekla List Manager - 现代化版本"
        Height="800"
        Width="1400"
        MinHeight="600"
        MinWidth="1000"
        WindowStartupLocation="CenterScreen"
        Topmost="{Binding IsTopMost}"
        Background="#F5F5F5"
        FontFamily="Segoe UI"
        FontSize="12">

    <Window.DataContext>
        <viewModels:MainViewModel />
    </Window.DataContext>

    <Window.Resources>
        <!-- 现代化样式资源 -->

        <!-- 主要颜色定义 -->
        <SolidColorBrush x:Key="PrimaryBrush" Color="#2196F3"/>
        <SolidColorBrush x:Key="PrimaryDarkBrush" Color="#1976D2"/>
        <SolidColorBrush x:Key="AccentBrush" Color="#FF4081"/>
        <SolidColorBrush x:Key="SurfaceBrush" Color="#FFFFFF"/>
        <SolidColorBrush x:Key="BackgroundBrush" Color="#F5F5F5"/>
        <SolidColorBrush x:Key="TextPrimaryBrush" Color="#212121"/>
        <SolidColorBrush x:Key="TextSecondaryBrush" Color="#757575"/>
        <SolidColorBrush x:Key="DividerBrush" Color="#E0E0E0"/>
        <SolidColorBrush x:Key="SuccessBrush" Color="#4CAF50"/>
        <SolidColorBrush x:Key="WarningBrush" Color="#FF9800"/>
        <SolidColorBrush x:Key="ErrorBrush" Color="#F44336"/>

        <!-- 现代化按钮样式 -->
        <Style x:Key="ModernButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="{StaticResource SurfaceBrush}"/>
            <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
            <Setter Property="BorderBrush" Value="{StaticResource DividerBrush}"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="12,8"/>
            <Setter Property="Margin" Value="4"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="4"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#E3F2FD"/>
                                <Setter Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#BBDEFB"/>
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter Property="Opacity" Value="0.6"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 主要操作按钮样式 -->
        <Style x:Key="PrimaryButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
            <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="4"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="{StaticResource PrimaryDarkBrush}"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#1565C0"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 现代化文本框样式 -->
        <Style x:Key="ModernTextBoxStyle" TargetType="TextBox">
            <Setter Property="Background" Value="{StaticResource SurfaceBrush}"/>
            <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
            <Setter Property="BorderBrush" Value="{StaticResource DividerBrush}"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="12,8"/>
            <Setter Property="Margin" Value="4"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="4"
                                Padding="{TemplateBinding Padding}">
                            <ScrollViewer x:Name="PART_ContentHost"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsFocused" Value="True">
                                <Setter Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
                                <Setter Property="BorderThickness" Value="2"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 现代化复选框样式 -->
        <Style x:Key="ModernCheckBoxStyle" TargetType="CheckBox">
            <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
            <Setter Property="Margin" Value="8,4"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="FontWeight" Value="Medium"/>
        </Style>

        <!-- 卡片样式 -->
        <Style x:Key="CardStyle" TargetType="Border">
            <Setter Property="Background" Value="{StaticResource SurfaceBrush}"/>
            <Setter Property="BorderBrush" Value="{StaticResource DividerBrush}"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="16"/>
            <Setter Property="Margin" Value="8"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Black" Opacity="0.1" ShadowDepth="2" BlurRadius="8"/>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <!-- 主布局容器 -->
    <Grid Background="{StaticResource BackgroundBrush}">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题栏 -->
        <Border Grid.Row="0" Background="{StaticResource PrimaryBrush}" Padding="20,12">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Text="📋" FontSize="24" Margin="0,0,12,0" VerticalAlignment="Center"/>
                    <StackPanel>
                        <TextBlock Text="Tekla List Manager"
                                   FontSize="18"
                                   FontWeight="SemiBold"
                                   Foreground="White"/>
                        <TextBlock Text="现代化零件和构件管理工具"
                                   FontSize="12"
                                   Foreground="#E3F2FD"
                                   Opacity="0.9"/>
                    </StackPanel>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <CheckBox Content="窗口置顶"
                              IsChecked="{Binding IsTopMost}"
                              Foreground="White"
                              Margin="0,0,16,0"
                              VerticalAlignment="Center"/>
                    <TextBlock Text="{Binding IsAssemblyMode, Converter={StaticResource BoolToModeTextConverter}}"
                               Background="#1976D2"
                               Foreground="White"
                               Padding="8,4"
                               FontWeight="Medium"
                               VerticalAlignment="Center">
                        <TextBlock.Style>
                            <Style TargetType="TextBlock">
                                <Setter Property="Background" Value="#1976D2"/>
                                <Style.Triggers>
                                    <DataTrigger Binding="{Binding IsAssemblyMode}" Value="True">
                                        <Setter Property="Background" Value="#4CAF50"/>
                                    </DataTrigger>
                                </Style.Triggers>
                            </Style>
                        </TextBlock.Style>
                    </TextBlock>
                </StackPanel>
            </Grid>
        </Border>

        <!-- 工具栏区域 -->
        <Border Grid.Row="1" Style="{StaticResource CardStyle}" Margin="8,8,8,4">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- 主要操作按钮 -->
                <WrapPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,12">
                    <Button Command="{Binding LoadAllPartsCommand}"
                            Style="{StaticResource PrimaryButtonStyle}"
                            ToolTip="从Tekla模型加载所有零件">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="�" FontSize="16" Margin="0,0,8,0"/>
                            <TextBlock Text="加载所有零件" FontWeight="SemiBold"/>
                        </StackPanel>
                    </Button>

                    <Button Command="{Binding LoadSelectedPartsCommand}"
                            Style="{StaticResource ModernButtonStyle}"
                            ToolTip="仅加载在Tekla中选中的零件">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="🎯" FontSize="16" Margin="0,0,8,0"/>
                            <TextBlock Text="加载选中零件"/>
                        </StackPanel>
                    </Button>

                    <Button Command="{Binding RefreshDataCommand}"
                            Style="{StaticResource ModernButtonStyle}"
                            ToolTip="清除缓存并重新加载数据">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="🔄" FontSize="16" Margin="0,0,8,0"/>
                            <TextBlock Text="刷新数据"/>
                        </StackPanel>
                    </Button>

                    <Button Command="{Binding ReconnectTeklaCommand}"
                            Style="{StaticResource ModernButtonStyle}"
                            ToolTip="重新连接到Tekla模型">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="🔗" FontSize="16" Margin="0,0,8,0"/>
                            <TextBlock Text="重新连接"/>
                        </StackPanel>
                    </Button>

                    <Border Width="1" Background="{StaticResource DividerBrush}" Margin="8,4"/>

                    <Button Command="{Binding ToggleAssemblyModeCommand}"
                            Style="{StaticResource ModernButtonStyle}"
                            ToolTip="在零件模式和构件模式之间切换">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="🔀" FontSize="16" Margin="0,0,8,0"/>
                            <TextBlock Text="切换模式"/>
                        </StackPanel>
                    </Button>
                </WrapPanel>

                <!-- 搜索和选项区域 -->
                <Grid Grid.Row="1">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="300"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <TextBlock Grid.Column="0"
                               Text="🔍"
                               FontSize="16"
                               VerticalAlignment="Center"
                               Margin="0,0,8,0"/>

                    <TextBox Grid.Column="1"
                             Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                             ToolTip="输入关键词搜索零件或构件"
                             Tag="搜索零件编号、名称、材质等...">
                        <TextBox.Style>
                            <Style TargetType="TextBox" BasedOn="{StaticResource ModernTextBoxStyle}">
                                <Style.Triggers>
                                    <Trigger Property="Text" Value="">
                                        <Setter Property="Background">
                                            <Setter.Value>
                                                <VisualBrush AlignmentX="Left" AlignmentY="Center" Stretch="None">
                                                    <VisualBrush.Visual>
                                                        <TextBlock Text="搜索零件编号、名称、材质等..."
                                                                   Foreground="{StaticResource TextSecondaryBrush}"
                                                                   Margin="12,0,0,0"/>
                                                    </VisualBrush.Visual>
                                                </VisualBrush>
                                            </Setter.Value>
                                        </Setter>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </TextBox.Style>
                    </TextBox>

                    <Button Grid.Column="2"
                            Command="{Binding SearchCommand}"
                            Style="{StaticResource ModernButtonStyle}"
                            HorizontalAlignment="Left"
                            Margin="8,0,0,0"
                            ToolTip="执行搜索">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="�" FontSize="14" Margin="0,0,4,0"/>
                            <TextBlock Text="搜索"/>
                        </StackPanel>
                    </Button>

                    <StackPanel Grid.Column="3" Orientation="Horizontal">
                        <CheckBox Content="启用高亮"
                                  IsChecked="{Binding EnableHighlight}"
                                  Style="{StaticResource ModernCheckBoxStyle}"
                                  ToolTip="启用/禁用选择列表时高亮显示模型功能"/>

                        <CheckBox Content="组合相同行"
                                  IsChecked="{Binding IsMergeRows}"
                                  Style="{StaticResource ModernCheckBoxStyle}"
                                  ToolTip="勾选后相同编号会合并为一行"/>
                    </StackPanel>
                </Grid>
            </Grid>
        </Border>

        <!-- 数据网格 -->
        <Grid Grid.Row="2">
            <!-- 优化的虚拟化数据网格 -->
            <views:VirtualizedDataGrid x:Name="PartsDataGrid"
                                     ItemsSource="{Binding PartsView, UpdateSourceTrigger=PropertyChanged}"
                                     IsLoading="{Binding IsLoading}"
                                     Visibility="{Binding IsAssemblyMode, Converter={StaticResource BoolToInverseVisibilityConverter}}"
                                     SelectionChanged="PartsDataGrid_SelectionChanged"
                                     RowDoubleClick="PartsDataGrid_RowDoubleClick"/>

            <views:VirtualizedDataGrid x:Name="AssembliesDataGrid"
                                     ItemsSource="{Binding AssembliesView, UpdateSourceTrigger=PropertyChanged}"
                                     IsLoading="{Binding IsLoading}"
                                     Visibility="{Binding IsAssemblyMode, Converter={StaticResource BoolToVisibilityConverter}}"
                                     SelectionChanged="AssembliesDataGrid_SelectionChanged"
                                     RowDoubleClick="AssembliesDataGrid_RowDoubleClick"/>
        </Grid>

        <!-- 状态栏 -->
        <StatusBar Grid.Row="3">
            <StatusBarItem>
                <TextBlock Text="{Binding StatusText}"/>
            </StatusBarItem>
            <Separator/>
            <StatusBarItem>
                <TextBlock Text="{Binding CountText}"/>
            </StatusBarItem>
            <Separator/>
            <StatusBarItem>
                <TextBlock Text="{Binding TimeText}"/>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</Window>
